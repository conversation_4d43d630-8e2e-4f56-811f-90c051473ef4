import React, { useState } from 'react';

function Contact() {
  const [formData, setFormData] = useState({
    fullName: '',
    phoneNumber: '',
    emailAddress: '',
    briefMessage: '',
  });

  const [errors, setErrors] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const validate = () => {
    let newErrors = {};
    if (!formData.fullName) newErrors.fullName = 'Full Name is required';
    if (!formData.phoneNumber) newErrors.phoneNumber = 'Phone Number is required';
    if (!formData.emailAddress) {
      newErrors.emailAddress = 'Email Address is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.emailAddress)) {
      newErrors.emailAddress = 'Email Address is invalid';
    }
    if (!formData.briefMessage) newErrors.briefMessage = 'Brief Message is required';
    return newErrors;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    setErrors(validationErrors);
    if (Object.keys(validationErrors).length === 0) {
      console.log('Form data submitted:', formData);
      setIsSubmitted(true);
      // Here you would typically send the data to a server
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 to-purple-100 py-12 px-4 sm:px-6 lg:px-8 flex flex-col items-center justify-center">
      <div className="max-w-4xl mx-auto text-center mb-12">
        <h1 className="text-5xl font-extrabold text-gray-900 sm:text-6xl lg:text-7xl leading-tight">
          Connect with Gentle Soul Caregiving
        </h1>
        <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
          We're here to answer your questions and provide the support you need. Reach out to us today.
        </p>
        <div className="mt-8">
          <a
            href="#schedule"
            className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-full shadow-lg text-white bg-indigo-600 hover:bg-indigo-700 transition duration-300 ease-in-out transform hover:-translate-y-1 hover:scale-105"
            aria-label="Schedule a Consultation"
          >
            Schedule a Consultation
          </a>
        </div>
      </div>

      <div className="w-full max-w-2xl bg-white p-8 rounded-lg shadow-xl" id="contact-form">
        <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Contact Us</h2>
        {isSubmitted ? (
          <div className="text-center text-green-600 font-semibold text-lg">
            Thank you for your message! We will get back to you shortly.
          </div>
        ) : (
          <form onSubmit={handleSubmit} noValidate>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700">
                  Full Name
                </label>
                <input
                  type="text"
                  name="fullName"
                  id="fullName"
                  value={formData.fullName}
                  onChange={handleChange}
                  className={`mt-1 block w-full px-4 py-2 border ${errors.fullName ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
                  aria-required="true"
                  aria-invalid={errors.fullName ? "true" : "false"}
                  aria-describedby={errors.fullName ? "fullName-error" : null}
                />
                {errors.fullName && <p id="fullName-error" className="mt-2 text-sm text-red-600">{errors.fullName}</p>}
              </div>
              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phoneNumber"
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  className={`mt-1 block w-full px-4 py-2 border ${errors.phoneNumber ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
                  aria-required="true"
                  aria-invalid={errors.phoneNumber ? "true" : "false"}
                  aria-describedby={errors.phoneNumber ? "phoneNumber-error" : null}
                />
                {errors.phoneNumber && <p id="phoneNumber-error" className="mt-2 text-sm text-red-600">{errors.phoneNumber}</p>}
              </div>
            </div>
            <div className="mt-6">
              <label htmlFor="emailAddress" className="block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <input
                type="email"
                name="emailAddress"
                id="emailAddress"
                value={formData.emailAddress}
                onChange={handleChange}
                className={`mt-1 block w-full px-4 py-2 border ${errors.emailAddress ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
                aria-required="true"
                aria-invalid={errors.emailAddress ? "true" : "false"}
                aria-describedby={errors.emailAddress ? "emailAddress-error" : null}
              />
              {errors.emailAddress && <p id="emailAddress-error" className="mt-2 text-sm text-red-600">{errors.emailAddress}</p>}
            </div>
            <div className="mt-6">
              <label htmlFor="briefMessage" className="block text-sm font-medium text-gray-700">
                Brief Message
              </label>
              <textarea
                name="briefMessage"
                id="briefMessage"
                rows="4"
                value={formData.briefMessage}
                onChange={handleChange}
                className={`mt-1 block w-full px-4 py-2 border ${errors.briefMessage ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
                aria-required="true"
                aria-invalid={errors.briefMessage ? "true" : "false"}
                aria-describedby={errors.briefMessage ? "briefMessage-error" : null}
              ></textarea>
              {errors.briefMessage && <p id="briefMessage-error" className="mt-2 text-sm text-red-600">{errors.briefMessage}</p>}
            </div>
            <div className="mt-8">
              <button
                type="submit"
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out transform hover:-translate-y-0.5 hover:scale-105"
              >
                Send Message
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

export default Contact;