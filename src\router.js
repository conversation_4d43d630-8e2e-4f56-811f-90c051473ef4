import React from 'react';
import { createBrowserRouter } from 'react-router-dom';

// Page Components
import Home from './pages/Home';
import AboutTanya from './pages/AboutTanya';
import Services from './pages/Services';
import Testimonials from './pages/Testimonials';
import Contact from './pages/Contact';

// Layout Components
import Header from './components/Header';
import Footer from './components/Footer';

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <>
        <Header />
        <Home />
        <Footer />
      </>
    ),
  },
  {
    path: '/about-tanya',
    element: (
      <>
        <Header />
        <AboutTanya />
        <Footer />
      </>
    ),
  },
  {
    path: '/services',
    element: (
      <>
        <Header />
        <Services />
        <Footer />
      </>
    ),
  },
  {
    path: '/testimonials',
    element: (
      <>
        <Header />
        <Testimonials />
        <Footer />
      </>
    ),
  },
  {
    path: '/contact',
    element: (
      <>
        <Header />
        <Contact />
        <Footer />
      </>
    ),
  },
]);

export default router;