import React from 'react';

const testimonialsData = [
  {
    id: 1,
    quote: "Gentle Soul Caregiving provided exceptional care for my mother. The caregivers were compassionate, professional, and truly made a difference in her quality of life.",
    author: "<PERSON>",
    city: "Chicago, IL"
  },
  {
    id: 2,
    quote: "I highly recommend Gentle Soul Caregiving. They are reliable, attentive, and genuinely care about their clients. My father's health and happiness improved significantly under their care.",
    author: "<PERSON>",
    city: "Naperville, IL"
  },
  {
    id: 3,
    quote: "The team at Gentle Soul Caregiving is outstanding. They went above and beyond to ensure my grandmother was comfortable and well-cared for. Their dedication is truly admirable.",
    author: "<PERSON>",
    city: "Aurora, IL"
  },
  {
    id: 4,
    quote: "Finding Gentle Soul Caregiving was a blessing. Their personalized approach to care made all the difference for my uncle. He felt respected and valued.",
    author: "<PERSON>",
    city: "Evanston, IL"
  },
];

function Testimonials() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-extrabold text-center text-gray-900 mb-4">
          What Our Clients Say
        </h2>
        <p className="text-xl text-center text-gray-600 mb-12">
          Hear from families who have experienced the compassionate care of Gentle Soul Caregiving.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonialsData.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-white rounded-lg shadow-lg p-8 flex flex-col justify-between"
              role="article"
              aria-labelledby={`testimonial-author-${testimonial.id}`}
            >
              <p className="text-gray-700 text-lg italic mb-6">"{testimonial.quote}"</p>
              <div>
                <h3 id={`testimonial-author-${testimonial.id}`} className="font-semibold text-gray-900 text-xl">
                  {testimonial.author}
                </h3>
                <p className="text-gray-600 text-md">{testimonial.city}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default Testimonials;