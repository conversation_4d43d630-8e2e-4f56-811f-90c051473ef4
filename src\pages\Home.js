import React from 'react';

function Home() {
  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section
        className="relative bg-cover bg-center h-screen flex items-center justify-center text-center"
        style={{ backgroundImage: "url('/hero-gentle-soul.jpg')" }}
        aria-label="Hero section with background image of a caregiver and senior"
      >
        <div className="absolute inset-0 bg-black opacity-50" aria-hidden="true"></div>
        <div className="relative z-10 text-white p-4 md:p-8">
          <h2 className="text-4xl md:text-6xl font-bold leading-tight mb-4" id="hero-headline">
            Your Comfort, Our Compassion
          </h2>
          <p className="text-xl md:text-2xl mb-8">
            19 Years of Solo, In-Home Care for Seniors 65+
          </p>
          <a
            href="/contact"
            className="inline-block bg-teal-600 hover:bg-teal-700 text-white font-bold py-3 px-8 rounded-full transition duration-300 ease-in-out transform hover:scale-105"
            role="button"
            aria-labelledby="hero-headline"
          >
            Learn More
          </a>
        </div>
      </section>

      {/* Why Choose Us Grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl md:text-4xl font-bold text-center text-gray-800 mb-12">
            Why Choose Us
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Card 1: Personalized Care */}
            <div className="bg-white rounded-lg shadow-lg p-8 text-center transform transition duration-300 hover:scale-105">
              <img
                src="/icons/heart-hands.svg"
                alt="Heart in hands icon"
                className="w-16 h-16 mx-auto mb-6"
                aria-hidden="true"
              />
              <h4 className="text-xl font-semibold text-gray-800 mb-4">Personalized Care</h4>
              <p className="text-gray-600">
                Every individual is unique, and so is our care. We tailor our services to meet your specific needs and preferences.
              </p>
            </div>

            {/* Card 2: Experienced & Compassionate */}
            <div className="bg-white rounded-lg shadow-lg p-8 text-center transform transition duration-300 hover:scale-105">
              <img
                src="/icons/star.svg"
                alt="Star icon"
                className="w-16 h-16 mx-auto mb-6"
                aria-hidden="true"
              />
              <h4 className="text-xl font-semibold text-gray-800 mb-4">Experienced & Compassionate</h4>
              <p className="text-gray-600">
                With 19 years of dedicated service, we bring expertise and genuine compassion to your home.
              </p>
            </div>

            {/* Card 3: Peace of Mind */}
            <div className="bg-white rounded-lg shadow-lg p-8 text-center transform transition duration-300 hover:scale-105">
              <img
                src="/icons/shield-check.svg"
                alt="Shield with checkmark icon"
                className="w-16 h-16 mx-auto mb-6"
                aria-hidden="true"
              />
              <h4 className="text-xl font-semibold text-gray-800 mb-4">Peace of Mind</h4>
              <p className="text-gray-600">
                Rest easy knowing your loved ones are in capable and caring hands, receiving the best in-home support.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Services Overview Section */}
      <section className="py-16 bg-blue-50" aria-labelledby="featured-services-heading">
        <div className="container mx-auto px-4">
          <h3 id="featured-services-heading" className="text-3xl md:text-4xl font-bold text-center text-gray-800 mb-12">
            Our Featured Services
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service Card 1: Personal Care */}
            <div className="bg-white rounded-lg shadow-lg p-8 transform transition duration-300 hover:scale-105" role="region" aria-labelledby="personal-care-heading">
              <h4 id="personal-care-heading" className="text-2xl font-semibold text-gray-800 mb-4 text-center">Personal Care</h4>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>Bathing and Grooming Assistance</li>
                <li>Dressing and Undressing Support</li>
                <li>Mobility and Transfer Assistance</li>
                <li>Medication Reminders</li>
                <li>Incontinence Care</li>
              </ul>
            </div>

            {/* Service Card 2: Companion Care */}
            <div className="bg-white rounded-lg shadow-lg p-8 transform transition duration-300 hover:scale-105" role="region" aria-labelledby="companion-care-heading">
              <h4 id="companion-care-heading" className="text-2xl font-semibold text-gray-800 mb-4 text-center">Companion Care</h4>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>Meal Preparation and Feeding</li>
                <li>Light Housekeeping and Laundry</li>
                <li>Grocery Shopping and Errands</li>
                <li>Transportation to Appointments</li>
                <li>Social Engagement and Activities</li>
              </ul>
            </div>

            {/* Service Card 3: Memory Care Support */}
            <div className="bg-white rounded-lg shadow-lg p-8 transform transition duration-300 hover:scale-105" role="region" aria-labelledby="memory-care-heading">
              <h4 id="memory-care-heading" className="text-2xl font-semibold text-gray-800 mb-4 text-center">Memory Care Support</h4>
              <ul className="list-disc list-inside text-gray-600 space-y-2">
                <li>Structured Routines and Activities</li>
                <li>Cognitive Stimulation Exercises</li>
                <li>Safe and Supportive Environment</li>
                <li>Behavioral Support and Redirecting</li>
                <li>Family Respite and Education</li>
              </ul>
            </div>
          </div>
        </div>
:start_line:127
-------
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-white" aria-labelledby="how-it-works-heading">
        <div className="container mx-auto px-4">
          <h3 id="how-it-works-heading" className="text-3xl md:text-4xl font-bold text-center text-gray-800 mb-12">
            How It Works
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Step 1: Connect with Tanya */}
            <div className="bg-blue-50 rounded-lg shadow-lg p-8 text-center transform transition duration-300 hover:scale-105" role="article" aria-labelledby="step1-heading">
              <img
                src="/icons/phone-call.svg"
                alt="Phone icon"
                className="w-16 h-16 mx-auto mb-6 text-teal-600"
                aria-hidden="true"
              />
              <h4 id="step1-heading" className="text-xl font-semibold text-gray-800 mb-4">1. Connect with Tanya</h4>
              <p className="text-gray-600">
                Reach out to Tanya directly via phone or email to discuss your care needs and schedule a convenient time for your free consultation.
              </p>
            </div>

            {/* Step 2: Free Consultation */}
            <div className="bg-blue-50 rounded-lg shadow-lg p-8 text-center transform transition duration-300 hover:scale-105" role="article" aria-labelledby="step2-heading">
              <img
                src="/icons/calendar-check.svg"
                alt="Calendar with checkmark icon"
                className="w-16 h-16 mx-auto mb-6 text-teal-600"
                aria-hidden="true"
              />
              <h4 id="step2-heading" className="text-xl font-semibold text-gray-800 mb-4">2. Free Consultation</h4>
              <p className="text-gray-600">
                Tanya will visit your home to understand your loved one's specific requirements, answer your questions, and assess the best care approach.
              </p>
            </div>

            {/* Step 3: Customized Care Plan */}
            <div className="bg-blue-50 rounded-lg shadow-lg p-8 text-center transform transition duration-300 hover:scale-105" role="article" aria-labelledby="step3-heading">
              <img
                src="/icons/document-text.svg"
                alt="Document icon"
                className="w-16 h-16 mx-auto mb-6 text-teal-600"
                aria-hidden="true"
              />
              <h4 id="step3-heading" className="text-xl font-semibold text-gray-800 mb-4">3. Customized Care Plan</h4>
              <p className="text-gray-600">
                Based on the consultation, Tanya will develop a personalized care plan tailored to your loved one's unique needs, ensuring comprehensive and compassionate support.
              </p>
            </div>

            {/* Step 4: Begin Compassionate Care */}
            <div className="bg-blue-50 rounded-lg shadow-lg p-8 text-center transform transition duration-300 hover:scale-105" role="article" aria-labelledby="step4-heading">
              <img
                src="/icons/home-heart.svg"
                alt="Home with heart icon"
                className="w-16 h-16 mx-auto mb-6 text-teal-600"
                aria-hidden="true"
              />
              <h4 id="step4-heading" className="text-xl font-semibold text-gray-800 mb-4">4. Begin Compassionate Care</h4>
              <p className="text-gray-600">
                Once the plan is approved, Tanya will begin providing dedicated in-home care, bringing comfort, dignity, and peace of mind to your family.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gray-100" aria-labelledby="testimonials-heading">
        <div className="container mx-auto px-4">
          <h3 id="testimonials-heading" className="text-3xl md:text-4xl font-bold text-center text-gray-800 mb-12">
            What Our Clients Say
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-white rounded-lg shadow-lg p-8 flex flex-col items-center text-center transform transition duration-300 hover:scale-105" role="article" aria-label="Client Testimonial">
              <img
                src="/testimonials/client-1.jpg"
                alt="Smiling senior woman"
                className="w-24 h-24 rounded-full mx-auto mb-6 object-cover"
              />
              <p className="text-gray-700 italic mb-4">
                "Gentle Soul Caregiving provided exceptional care for my mother. Tanya's compassion and professionalism made all the difference. Highly recommended!"
              </p>
              <p className="font-semibold text-gray-800">- Sarah P.</p>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white rounded-lg shadow-lg p-8 flex flex-col items-center text-center transform transition duration-300 hover:scale-105" role="article" aria-label="Client Testimonial">
              <img
                src="/testimonials/client-2.jpg"
                alt="Smiling senior man"
                className="w-24 h-24 rounded-full mx-auto mb-6 object-cover"
              />
              <p className="text-gray-700 italic mb-4">
                "The level of personalized care is outstanding. My father felt comfortable and well-cared for, which gave our family immense peace of mind."
              </p>
              <p className="font-semibold text-gray-800">- John D.</p>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white rounded-lg shadow-lg p-8 flex flex-col items-center text-center transform transition duration-300 hover:scale-105" role="article" aria-label="Client Testimonial">
              <img
                src="/testimonials/client-3.jpg"
                alt="Smiling senior couple"
                className="w-24 h-24 rounded-full mx-auto mb-6 object-cover"
              />
              <p className="text-gray-700 italic mb-4">
                "Tanya is truly a gem. Her dedication and gentle approach made our daily lives so much easier. We are incredibly grateful for her support."
              </p>
              <p className="font-semibold text-gray-800">- Emily R.</p>
            </div>

            {/* Testimonial 4 (Optional) */}
            <div className="bg-white rounded-lg shadow-lg p-8 flex flex-col items-center text-center transform transition duration-300 hover:scale-105" role="article" aria-label="Client Testimonial">
              <img
                src="/testimonials/client-4.jpg"
                alt="Smiling senior woman with glasses"
                className="w-24 h-24 rounded-full mx-auto mb-6 object-cover"
              />
              <p className="text-gray-700 italic mb-4">
                "Professional, reliable, and genuinely caring. Gentle Soul Caregiving exceeded our expectations in every way. Thank you for everything!"
              </p>
              <p className="font-semibold text-gray-800">- Michael S.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Home;