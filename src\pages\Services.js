import React from 'react';

function Services() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-extrabold text-gray-900 text-center mb-12">Our Services</h1>

        {/* Personal Care Section */}
        <section className="bg-white shadow-lg rounded-lg p-8 mb-8" aria-labelledby="personal-care-heading">
          <h2 id="personal-care-heading" className="text-3xl font-semibold text-indigo-700 mb-6">Personal Care</h2>
          <ul className="list-disc list-inside text-gray-700 space-y-3 text-lg">
            <li>Assistance with bathing, dressing, and grooming</li>
            <li>Medication reminders and management</li>
            <li>Mobility assistance and transfers</li>
            <li>Incontinence care</li>
            <li>Meal preparation and feeding assistance</li>
          </ul>
        </section>

        {/* Companion Care Section */}
        <section className="bg-white shadow-lg rounded-lg p-8 mb-8" aria-labelledby="companion-care-heading">
          <h2 id="companion-care-heading" className="text-3xl font-semibold text-indigo-700 mb-6">Companion Care</h2>
          <ul className="list-disc list-inside text-gray-700 space-y-3 text-lg">
            <li>Companionship and social interaction</li>
            <li>Light housekeeping and laundry</li>
            <li>Errands and grocery shopping</li>
            <li>Transportation to appointments</li>
            <li>Hobby and activity engagement</li>
          </ul>
        </section>

        {/* Memory Care Support Section */}
        <section className="bg-white shadow-lg rounded-lg p-8" aria-labelledby="memory-care-heading">
          <h2 id="memory-care-heading" className="text-3xl font-semibold text-indigo-700 mb-6">Memory Care Support</h2>
          <ul className="list-disc list-inside text-gray-700 space-y-3 text-lg">
            <li>Specialized care for individuals with dementia and Alzheimer's</li>
            <li>Cognitive stimulation activities</li>
            <li>Behavioral support and redirection</li>
            <li>Safe and supportive environment creation</li>
            <li>Family caregiver respite</li>
          </ul>
        </section>
      </div>
    </div>
  );
}

export default Services;